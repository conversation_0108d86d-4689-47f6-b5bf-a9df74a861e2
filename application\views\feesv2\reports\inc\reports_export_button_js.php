<script>
    function toggleFilters() {
        const filtersContainer = document.getElementById("filtersContainer");
        const toggleIcon = document.getElementById("filterToggleIcon");

        // Close any open dropdowns before toggling
        $('.bootstrap-select').removeClass('open');
        $('.dropdown-menu').removeClass('show');

        if (filtersContainer.classList.contains('hiding')) {
            // Show filters
            filtersContainer.style.maxHeight = filtersContainer.scrollHeight + 'px';
            filtersContainer.classList.remove('hiding');

            // Keep icon as chevron-down when shown
            toggleIcon.classList.remove('bi-chevron-down');
            toggleIcon.classList.add('bi-chevron-up');

            // Reset max-height after animation completes and refresh selectpickers
            setTimeout(() => {
                filtersContainer.style.maxHeight = 'none';
                $('.selectpicker').selectpicker('refresh');
            }, 400);
        } else {
            // Hide filters
            filtersContainer.style.maxHeight = filtersContainer.scrollHeight + 'px';
            filtersContainer.offsetHeight;

            requestAnimationFrame(() => {
                filtersContainer.classList.add('hiding');

                // Change to chevron-up when hidden
                toggleIcon.classList.remove('bi-chevron-up');
                toggleIcon.classList.add('bi-chevron-down');
            });
        }
    }


    function toggleMenu() {
        const menu = document.getElementById("filterMenu");
        const button = document.querySelector(".menu-button");

        if (menu && button) {
            const isOpen = menu.style.display === "block";
            menu.style.display = isOpen ? "none" : "block";
            button.classList.toggle("active", !isOpen);
        }
    }

    document.addEventListener("click", function(event) {
        const menu = document.getElementById("filterMenu");
        const button = document.querySelector(".menu-button");
        const wrapper = document.querySelector(".menu-wrapper");

        if (
            menu && menu.style.display === "block" &&
            wrapper && !wrapper.contains(event.target)
        ) {
            menu.style.display = "none";
            if (button) button.classList.remove("active");
        }
    });
</script>